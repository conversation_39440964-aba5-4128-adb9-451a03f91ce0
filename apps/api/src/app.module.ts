import { ExecutionContext, Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { CoreModule } from '@app/core';
import { ClsModule, ClsService } from 'nestjs-cls';
import { APP_FILTER, APP_GUARD } from '@nestjs/core';
import { AllExceptionsFilter } from '@app/common/filters';
import { UserModule } from './user/user.module';
import { AuthModule } from './auth/auth.module';
import { BrokerModule } from './broker/broker.module';
import { AuthGuard, RolesGuard } from '@app/auth';
import { ThrottlerModule, ThrottlerGuard } from '@nestjs/throttler';
import { EnvService } from '@app/core/env';

export const clsSetupHelper = (cls: ClsService, context: ExecutionContext) => {
  try {
    let session: Record<string, unknown> | null = null;

    if (context.getType() === 'rpc') {
      const rpcData = context.switchToRpc().getData();
      session = rpcData.headers?.session || null;
      cls.set('session', session);
    }
    if (context.getType() === 'ws') {
      const client = context.switchToWs().getClient();
      session = client.request?.session || null;
      cls.set('session', session);
    }
  } catch (e: unknown) {
    const error = e instanceof Error ? e : new Error('Unknown error occurred');
    throw new Error(error.message);
  }
};

@Module({
  imports: [
    CoreModule,
    ClsModule.forRoot({
      global: true,
      guard: {
        mount: true,
        setup: clsSetupHelper,
      },
      middleware: {
        mount: true,
        generateId: true,
        setup: (cls, req) => {
          cls.set('session', req.session);
        },
        useEnterWith: true,
      },
    }),
    // Throttler configuration for rate limiting
    ThrottlerModule.forRootAsync({
      inject: [EnvService],
      useFactory: (envService: EnvService) => [
        {
          ttl: envService.get('BROKER_RATE_LIMIT_WINDOW'),
          limit: envService.get('BROKER_RATE_LIMIT_REQUESTS'),
        },
      ],
    }),
    UserModule, // Import User API module for user management endpoints
    AuthModule, // Import Auth API module for authentication endpoints
    BrokerModule, // Import Broker API module for broker management endpoints
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_FILTER,
      useClass: AllExceptionsFilter,
    },
    // Global guards configuration - AuthGuard runs first, then RolesGuard, then ThrottlerGuard
    // Requirements: 6.1, 6.2 - Global authentication and authorization
    // Requirements: 4.4 - Rate limiting for security
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
    {
      provide: APP_GUARD,
      useClass: RolesGuard,
    },
    {
      provide: APP_GUARD,
      useClass: ThrottlerGuard,
    },
  ],
})
export class AppModule {}
