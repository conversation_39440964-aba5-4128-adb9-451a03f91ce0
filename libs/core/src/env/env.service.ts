import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService as NestConfigService } from '@nestjs/config';
import { EnvType, EnvSchema } from './env.schema';
import { EnvError } from './env.error';

@Injectable()
export class EnvService implements OnModuleInit {
  private readonly logger = new Logger(EnvService.name);
  private env!: EnvType;

  constructor(private readonly nestConfigService: NestConfigService<EnvType, true>) {
    // Initialize coreConfig with default values - will be properly set in onModuleInit
    this.env = {} as EnvType;
  }

  onModuleInit() {
    this.logger.log('Initializing environment configuration...');

    try {
      // Validate core environment variables
      this.validateEnv();

      this.logger.log('Environment configuration initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize environment configuration', error);
      throw new EnvError('VALIDATION_FAILED', {
        message: 'Environment configuration initialization failed',
        cause: error,
      });
    }
  }

  /**
   * Get core environment variable with type safety
   */
  get<T extends keyof EnvType>(key: T): EnvType[T] {
    try {
      return this.nestConfigService.getOrThrow(key, { infer: true });
    } catch (error) {
      throw new EnvError('VALIDATION_FAILED', {
        message: `Core environment variable '${key}' is not defined`,
        cause: error,
      });
    }
  }

  /**
   * Get arbitrary environment variable (use with caution)
   * This method should only be used for environment variables that are not part of the core schema
   * @param key Environment variable name
   * @param defaultValue Optional default value
   * @returns Environment variable value or undefined
   */
  getArbitrary(key: string, defaultValue?: string): string | undefined {
    try {
      // Use process.env directly for arbitrary keys since ConfigService is typed for core keys only
      const value = process.env[key];
      return value !== undefined ? String(value) : defaultValue;
    } catch (error) {
      this.logger.warn(`Failed to get arbitrary environment variable '${key}':`, error);
      return defaultValue;
    }
  }

  /**
   * Get all core configuration
   */
  getAllEnv(): EnvType {
    if (!this.env) {
      this.validateEnv();
    }
    return this.env;
  }

  /**
   * Validate core environment variables
   */
  private validateEnv(): void {
    try {
      const rawEnv = {
        NODE_ENV: this.nestConfigService.get('NODE_ENV'),
        PORT: this.nestConfigService.get('PORT'),
        DB_HOST: this.nestConfigService.get('DB_HOST'),
        DB_PORT: this.nestConfigService.get('DB_PORT'),
        DB_NAME: this.nestConfigService.get('DB_NAME'),
        DB_USERNAME: this.nestConfigService.get('DB_USERNAME'),
        DB_PASSWORD: this.nestConfigService.get('DB_PASSWORD'),
        DB_SSL: this.nestConfigService.get('DB_SSL'),
        REDIS_HOST: this.nestConfigService.get('REDIS_HOST'),
        REDIS_PORT: this.nestConfigService.get('REDIS_PORT'),
        REDIS_USERNAME: this.nestConfigService.get('REDIS_USERNAME'),
        REDIS_PASSWORD: this.nestConfigService.get('REDIS_PASSWORD'),
        REDIS_URL: this.nestConfigService.get('REDIS_URL'),
        QUESTDB_HOST: this.nestConfigService.get('QUESTDB_HOST'),
        QUESTDB_PORT: this.nestConfigService.get('QUESTDB_PORT'),
        QUESTDB_USERNAME: this.nestConfigService.get('QUESTDB_USERNAME'),
        QUESTDB_PASSWORD: this.nestConfigService.get('QUESTDB_PASSWORD'),
        QUESTDB_DATABASE: this.nestConfigService.get('QUESTDB_DATABASE'),
        QUESTDB_SSL: this.nestConfigService.get('QUESTDB_SSL'),
        QUESTDB_MAX_CONNECTIONS: this.nestConfigService.get('QUESTDB_MAX_CONNECTIONS'),
        JWT_SECRET: this.nestConfigService.get('JWT_SECRET'),
        SESSION_SECRET_KEY: this.nestConfigService.get('SESSION_SECRET_KEY'),
        ANTHROPIC_API_KEY: this.nestConfigService.get('ANTHROPIC_API_KEY'),
        OPENAI_API_KEY: this.nestConfigService.get('OPENAI_API_KEY'),
        ANALYSER_SERVICE_URL: this.nestConfigService.get('ANALYSER_SERVICE_URL'),
        OMS_SERVICE_URL: this.nestConfigService.get('OMS_SERVICE_URL'),
        TICKER_SERVICE_URL: this.nestConfigService.get('TICKER_SERVICE_URL'),
        SIMULATOR_SERVICE_URL: this.nestConfigService.get('SIMULATOR_SERVICE_URL'),

        // Broker Management
        BROKER_ENCRYPTION_KEY: this.nestConfigService.get('BROKER_ENCRYPTION_KEY'),
        BROKER_ENCRYPTION_SALT: this.nestConfigService.get('BROKER_ENCRYPTION_SALT'),
        BROKER_KEY_ROTATION_INTERVAL: this.nestConfigService.get('BROKER_KEY_ROTATION_INTERVAL'),
        API_BASE_URL: this.nestConfigService.get('API_BASE_URL'),

        // Broker Health Monitoring
        BROKER_HEALTH_CHECK_INTERVAL: this.nestConfigService.get('BROKER_HEALTH_CHECK_INTERVAL'),
        BROKER_CONNECTION_TIMEOUT: this.nestConfigService.get('BROKER_CONNECTION_TIMEOUT'),
        BROKER_MAX_RETRY_ATTEMPTS: this.nestConfigService.get('BROKER_MAX_RETRY_ATTEMPTS'),

        // Broker Security
        BROKER_SESSION_TIMEOUT: this.nestConfigService.get('BROKER_SESSION_TIMEOUT'),
        BROKER_RATE_LIMIT_REQUESTS: this.nestConfigService.get('BROKER_RATE_LIMIT_REQUESTS'),
        BROKER_RATE_LIMIT_WINDOW: this.nestConfigService.get('BROKER_RATE_LIMIT_WINDOW'),
      };

      this.env = EnvSchema.parse(rawEnv);
      this.logger.log('Core environment configuration validated successfully');
    } catch (error) {
      this.logger.error('Core environment configuration validation failed', error);
      throw new EnvError('VALIDATION_FAILED', {
        message: 'Core environment configuration validation failed',
        cause: error,
      });
    }
  }
}
