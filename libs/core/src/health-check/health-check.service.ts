import { Injectable } from '@nestjs/common';
import { DateTimeUtilsService } from '@app/utils';

/**
 * Health status interface for detailed health information
 */
export interface HealthStatus {
  appStatus: boolean;
  postgresStatus: boolean;
  questdbStatus: boolean;
}

/**
 * Detailed health information with timestamps and statistics
 */
export interface DetailedHealthStatus extends HealthStatus {
  lastUpdated: string;
  services: {
    postgres: {
      status: boolean;
      lastCheck?: string;
    };
    questdb: {
      status: boolean;
      lastCheck?: string;
      connectionStats?: {
        totalConnections: number;
        idleConnections: number;
        waitingCount: number;
        healthCheckCount: number;
        failedHealthChecks: number;
      };
    };
  };
}

@Injectable()
export class HealthCheckService {
  private appStatus: boolean = false;
  private postgresStatus: boolean = false;
  private questdbStatus: boolean = false;
  private lastPostgresCheck?: Date;
  private lastQuestdbCheck?: Date;
  private questdbConnectionStats?: DetailedHealthStatus['services']['questdb']['connectionStats'];

  constructor(private readonly dateTimeUtils: DateTimeUtilsService) {}

  /**
   * Calculate overall app status based on individual service statuses
   */
  private setAppStatus(): void {
    // App is healthy if both PostgreSQL and QuestDB are healthy
    this.appStatus = this.postgresStatus && this.questdbStatus;
  }

  /**
   * Get overall application status
   */
  getAppStatus(): boolean {
    return this.appStatus;
  }

  /**
   * Set PostgreSQL status
   */
  setPostgresStatus(status: boolean): void {
    this.postgresStatus = status;
    this.lastPostgresCheck = this.dateTimeUtils.getNewDate();
    this.setAppStatus();
  }

  /**
   * Set QuestDB status
   */
  setQuestdbStatus(
    status: boolean,
    connectionStats?: DetailedHealthStatus['services']['questdb']['connectionStats'],
  ): void {
    this.questdbStatus = status;
    this.lastQuestdbCheck = this.dateTimeUtils.getNewDate();
    if (connectionStats) {
      this.questdbConnectionStats = connectionStats;
    }
    this.setAppStatus();
  }

  /**
   * Get basic status details
   */
  getAllStatusDetails(): HealthStatus {
    return {
      appStatus: this.appStatus,
      postgresStatus: this.postgresStatus,
      questdbStatus: this.questdbStatus,
    };
  }

  /**
   * Get detailed health status with timestamps and statistics
   */
  getDetailedHealthStatus(): DetailedHealthStatus {
    return {
      appStatus: this.appStatus,
      postgresStatus: this.postgresStatus,
      questdbStatus: this.questdbStatus,
      lastUpdated: this.dateTimeUtils.getUtcNow(),
      services: {
        postgres: {
          status: this.postgresStatus,
          lastCheck: this.lastPostgresCheck?.toISOString(),
        },
        questdb: {
          status: this.questdbStatus,
          lastCheck: this.lastQuestdbCheck?.toISOString(),
          connectionStats: this.questdbConnectionStats,
        },
      },
    };
  }

  /**
   * Get QuestDB specific status
   */
  getQuestdbStatus(): boolean {
    return this.questdbStatus;
  }

  /**
   * Get PostgreSQL specific status
   */
  getPostgresStatus(): boolean {
    return this.postgresStatus;
  }
}
